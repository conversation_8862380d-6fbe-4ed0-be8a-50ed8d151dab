import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   CreateDriverRequest,
   DriverOtpVerificationRequest,
   DriverRegistrationRequest,
   DriverResendOtpRequest,
   DriverResponse,
   UpdateDriverRequest,
} from '../types/driver';

/**
 * Hook for creating a new driver profile (after OTP verification)
 */
export const useCreateDriver = () => {
   return useMutation({
      mutationFn: async (data: CreateDriverRequest): Promise<DriverResponse> => {
         return apiClient.post('/drivers/admin', data);
      },
   });
};

/**
 * Hook for updating a driver profile
 */
export const useUpdateDriver = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateDriverRequest): Promise<DriverResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/drivers/admin/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a driver
 */
export const useDeleteDriver = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/drivers/admin/${id}`);
      },
   });
};

/**
 * Hook for registering a new driver with phone number
 */
export const useRegisterDriver = () => {
   return useMutation({
      mutationFn: async (
         data: DriverRegistrationRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/register', data);
      },
   });
};

/**
 * Hook for verifying driver OTP
 */
export const useVerifyDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverOtpVerificationRequest
      ): Promise<{ success: boolean; message: string; data?: any; timestamp: number }> => {
         return apiClient.post('/drivers/admin/verify-otp', data);
      },
   });
};

/**
 * Hook for resending driver OTP
 */
export const useResendDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverResendOtpRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/resend-otp', data);
      },
   });
};
