'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { ErrorMessage } from '@/components/error-message';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, Controller } from 'react-hook-form';
import * as z from 'zod';
import { useRegisterDriver } from '../api/mutations';
import PhoneInput, { isValidPhoneNumber, parsePhoneNumber } from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

// Phone number validation schema using react-phone-number-input validation
const phoneRegistrationSchema = z.object({
   mobile: z.string().refine(value => isValidPhoneNumber(value), {
      message: 'Invalid phone number',
   }),
});

type PhoneRegistrationFormValues = z.infer<typeof phoneRegistrationSchema>;

interface DriverRegistrationProps {
   onSuccess: (phoneNumber: string, userId: string) => void;
   onCancel: () => void;
}

export const DriverRegistration = ({ onSuccess, onCancel }: DriverRegistrationProps) => {
   const registerDriverMutation = useRegisterDriver();

   const form = useForm<PhoneRegistrationFormValues>({
      resolver: zodResolver(phoneRegistrationSchema),
      defaultValues: {
         mobile: '',
      },
   });

   const {
      control,
      handleSubmit,
      formState: { errors },
   } = form;

   const onSubmit = async (data: PhoneRegistrationFormValues) => {
      const phoneNumber = parsePhoneNumber(data.mobile);
      const formattedPhoneNumber = phoneNumber?.number || data.mobile;

      registerDriverMutation.mutate(
         {
            phoneNumber: formattedPhoneNumber,
         },
         {
            onSuccess: response => {
               toast.success('OTP sent successfully to your phone number');
               // Extract userId from the response data
               const userId = response.data?.userId;
               if (userId) {
                  onSuccess(formattedPhoneNumber, userId);
               } else {
                  toast.error('Registration successful but user ID not received');
                  onSuccess(formattedPhoneNumber, ''); // Fallback with empty userId
               }
            },
            onError: (error: any) => {
               toast.error(error?.message || 'Failed to send OTP. Please try again.');
            },
         }
      );
   };

   return (
      <div className='space-y-6'>
         <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
               <FormField
                  control={control}
                  name='mobile'
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Phone Number *</FormLabel>
                        <FormControl>
                           <Controller
                              name='mobile'
                              control={control}
                              render={({ field }) => (
                                 <PhoneInput
                                    {...field}
                                    international
                                    countryCallingCodeEditable={false}
                                    defaultCountry='IN'
                                    value={field.value}
                                    limitMaxLength={true}
                                    onChange={value => {
                                       field.onChange(value);
                                    }}
                                    disabled={registerDriverMutation.isPending}
                                    className='flex h-10 outline-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0 w-full rounded-md border border-input bg-background px-3 py-0 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none   disabled:cursor-not-allowed disabled:opacity-50'
                                 />
                              )}
                           />
                        </FormControl>
                        <ErrorMessage error={errors.mobile} />
                        <p className='text-xs text-muted-foreground'>
                           Please select your country and enter your phone number
                        </p>
                     </FormItem>
                  )}
               />

               <div className='flex gap-3 pt-4'>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onCancel}
                     disabled={registerDriverMutation.isPending}
                     className='flex-1'
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={registerDriverMutation.isPending}
                     className='flex-1'
                  >
                     {registerDriverMutation.isPending ? (
                        <>
                           Sending OTP...
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : (
                        'Send OTP'
                     )}
                  </Button>
               </div>
            </form>
         </Form>
      </div>
   );
};
